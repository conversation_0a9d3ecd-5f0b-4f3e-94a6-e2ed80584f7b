# 购物车功能测试用例

## 1. 购物车显示功能测试

### 1.1 未登录状态测试

#### TC001 - 未登录用户访问购物车页面
**测试用例ID**: TC001  
**测试标题**: 验证未登录用户访问购物车时的提示信息  
**优先级**: 高  

**前置条件**:
- 用户未登录系统
- 用户访问购物车页面

**测试步骤**:
1. 打开购物车页面
2. 检查页面显示内容

**预期结果**:
- 显示登录提示信息："购物车内暂时没有商品，登录后将显示您之前加入的商品"
- 提供登录入口或按钮

---

### 1.2 已登录状态测试

#### TC002 - 已登录用户购物车为空
**测试用例ID**: TC002  
**测试标题**: 验证已登录用户购物车为空时的显示  
**优先级**: 高  

**前置条件**:
- 用户已成功登录
- 购物车中没有任何商品

**测试步骤**:
1. 登录系统
2. 访问购物车页面
3. 检查页面显示内容

**预期结果**:
- 显示购物车为空的提示
- 提供"去购物"的引导按钮或链接

---

#### TC003 - 已登录用户购物车有商品
**测试用例ID**: TC003  
**测试标题**: 验证购物车商品信息正确显示  
**优先级**: 高  

**前置条件**:
- 用户已成功登录
- 购物车中已有商品

**测试步骤**:
1. 登录系统
2. 向购物车添加商品
3. 访问购物车页面
4. 检查商品信息显示

**预期结果**:
- 显示商品图片、名称、价格等基本信息
- 显示商品数量和小计金额
- 显示总计信息

---

### 1.3 价格计算测试

#### TC004 - 商品价格一致性验证
**测试用例ID**: TC004  
**测试标题**: 验证购物车中商品价格与加入时一致  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 商品详情页显示价格为100元

**测试步骤**:
1. 在商品详情页查看商品价格
2. 点击"加入购物车"
3. 进入购物车页面
4. 检查商品价格显示

**预期结果**:
- 购物车中显示的商品价格与加入时的价格完全一致

---

#### TC005 - 小计金额计算验证
**测试用例ID**: TC005  
**测试标题**: 验证商品小计计算正确性  
**优先级**: 高  

**测试数据**:
- 商品单价: 50元
- 商品数量: 3件

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 在购物车中选择数量为3的商品（单价50元）
2. 检查小计显示

**预期结果**:
- 小计显示为150元（50 × 3 = 150）

---

#### TC006 - 已选择商品件数统计
**测试用例ID**: TC006  
**测试标题**: 验证已选择商品件数统计正确  
**优先级**: 中  

**测试数据**:
- 商品A: 2件（已选中）
- 商品B: 3件（已选中）
- 商品C: 1件（未选中）

**前置条件**:
- 用户已登录
- 购物车中有多种商品

**测试步骤**:
1. 选中商品A和商品B
2. 不选中商品C
3. 检查"已选择n件商品"显示

**预期结果**:
- 显示"已选择5件商品"（2+3=5）

---

#### TC007 - 总价计算验证
**测试用例ID**: TC007  
**测试标题**: 验证购物车总价计算正确  
**优先级**: 高  

**测试数据**:
- 商品A: 单价30元，数量2件，已选中
- 商品B: 单价40元，数量1件，已选中
- 商品C: 单价20元，数量3件，未选中

**前置条件**:
- 用户已登录
- 购物车中有多种商品

**测试步骤**:
1. 选中商品A和商品B
2. 不选中商品C
3. 检查总价显示

**预期结果**:
- 总价显示为100元（30×2 + 40×1 = 100）

---

### 1.4 页面跳转功能测试

#### TC008 - 商品图片点击跳转
**测试用例ID**: TC008  
**测试标题**: 验证点击商品图片跳转到商品详情页  
**优先级**: 中  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 在购物车页面点击商品图片
2. 检查页面跳转

**预期结果**:
- 成功跳转到对应商品的详情页面

---

#### TC009 - 商品名称点击跳转
**测试用例ID**: TC009  
**测试标题**: 验证点击商品名称跳转到商品详情页  
**优先级**: 中  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 在购物车页面点击商品名称
2. 检查页面跳转

**预期结果**:
- 成功跳转到对应商品的详情页面

---

#### TC010 - 去结算按钮功能
**测试用例ID**: TC010  
**测试标题**: 验证点击去结算按钮跳转到订单页面  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有已选中的商品

**测试步骤**:
1. 选中购物车中的商品
2. 点击"去结算"按钮
3. 检查页面跳转

**预期结果**:
- 成功跳转到填写核对订单页面

---

#### TC011 - 已节省金额显示
**测试用例ID**: TC011  
**测试标题**: 验证抢购秒杀商品优惠金额显示  
**优先级**: 中  

**测试数据**:
- 秒杀商品原价: 100元
- 秒杀价格: 80元
- 商品数量: 2件

**前置条件**:
- 用户已登录
- 购物车中有抢购秒杀商品

**测试步骤**:
1. 将秒杀商品加入购物车
2. 选中该商品
3. 检查"已节省"金额显示

**预期结果**:
- 显示"已节省40元"（(100-80)×2 = 40）

---

## 2. 购物车添加商品功能测试

### 2.1 基本添加功能测试

#### TC012 - 从商品页面加入购物车
**测试用例ID**: TC012  
**测试标题**: 验证从商品详情页添加商品到购物车  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 在商品详情页面

**测试步骤**:
1. 在商品详情页面点击"加入购物车"按钮
2. 进入购物车页面检查

**预期结果**:
- 商品成功添加到购物车
- 购物车中显示该商品信息

---

### 2.2 数量编辑功能测试

#### TC013 - 直接输入数量
**测试用例ID**: TC013  
**测试标题**: 验证直接输入购买数量功能  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 在商品详情页面或购物车页面

**测试步骤**:
1. 在数量输入框中直接输入数字"5"
2. 确认输入

**预期结果**:
- 数量成功设置为5
- 相关计算（小计等）自动更新

---

#### TC014 - 使用加号按钮增加数量
**测试用例ID**: TC014  
**测试标题**: 验证点击+号按钮增加商品数量  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 商品当前数量为1

**测试步骤**:
1. 点击数量旁的"+"按钮
2. 检查数量变化

**预期结果**:
- 商品数量增加到2
- 小计金额相应更新

---

#### TC015 - 使用减号按钮减少数量
**测试用例ID**: TC015  
**测试标题**: 验证点击-号按钮减少商品数量  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 商品当前数量为3

**测试步骤**:
1. 点击数量旁的"-"按钮
2. 检查数量变化

**预期结果**:
- 商品数量减少到2
- 小计金额相应更新

---

### 2.3 数量限制测试

#### TC016 - 最小数量限制测试
**测试用例ID**: TC016  
**测试标题**: 验证商品数量不能小于1  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 商品当前数量为1

**测试步骤**:
1. 尝试点击"-"按钮
2. 检查按钮状态和数量

**预期结果**:
- "-"按钮处于失效状态，无法点击
- 商品数量保持为1

---

#### TC017 - 最大数量限制测试（200件）
**测试用例ID**: TC017  
**测试标题**: 验证商品数量不能超过200件  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 商品当前数量为200
- 商品库存充足

**测试步骤**:
1. 尝试点击"+"按钮
2. 检查按钮状态和数量

**预期结果**:
- "+"按钮处于失效状态，无法点击
- 商品数量保持为200

---

#### TC018 - 库存数量限制测试
**测试用例ID**: TC018  
**测试标题**: 验证商品数量不能超过库存  
**优先级**: 高  

**测试数据**:
- 商品库存: 5件
- 当前购物车数量: 5件

**前置条件**:
- 用户已登录
- 商品库存为5件
- 购物车中该商品数量为5件

**测试步骤**:
1. 尝试点击"+"按钮
2. 检查按钮状态和数量

**预期结果**:
- "+"按钮处于失效状态，无法点击
- 商品数量保持为5

---

#### TC019 - 直接输入超出范围数量测试
**测试用例ID**: TC019  
**测试标题**: 验证直接输入超出范围数量的处理  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 在购物车页面

**测试步骤**:
1. 在数量输入框中输入"0"
2. 确认输入
3. 检查系统反应

**预期结果**:
- 显示错误提示信息
- 数量恢复到有效值（如1）

---

#### TC020 - 直接输入超过200的数量测试
**测试用例ID**: TC020  
**测试标题**: 验证直接输入超过200的数量处理  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 在购物车页面

**测试步骤**:
1. 在数量输入框中输入"250"
2. 确认输入
3. 检查系统反应

**预期结果**:
- 显示错误提示信息
- 数量恢复到有效值（如200或库存数量）

---

### 2.4 购物车商品种类限制测试

#### TC021 - 商品种类数量限制测试
**测试用例ID**: TC021  
**测试标题**: 验证购物车商品种类不能超过20种  
**优先级**: 中  

**前置条件**:
- 用户已登录
- 购物车中已有20种不同商品

**测试步骤**:
1. 尝试添加第21种商品到购物车
2. 检查系统反应

**预期结果**:
- 显示提示信息，告知商品种类已达上限
- 第21种商品添加失败

---

## 3. 购物车删除商品功能测试

### 3.1 单个商品删除测试

#### TC022 - 删除单个商品
**测试用例ID**: TC022  
**测试标题**: 验证删除购物车中单个商品  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有多个商品

**测试步骤**:
1. 点击某个商品操作栏中的"删除"按钮
2. 确认删除操作
3. 检查购物车内容

**预期结果**:
- 对应商品从购物车中被删除
- 其他商品保持不变
- 总价等信息自动更新

---

### 3.2 批量删除测试

#### TC023 - 删除选中的多个商品
**测试用例ID**: TC023  
**测试标题**: 验证批量删除选中商品功能  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有多个商品

**测试步骤**:
1. 勾选多个商品（不是全部）
2. 点击"删除选中商品"按钮
3. 确认删除操作
4. 检查购物车内容

**预期结果**:
- 选中的商品全部被删除
- 未选中的商品保持不变
- 相关统计信息自动更新

---

#### TC024 - 全选删除功能测试
**测试用例ID**: TC024  
**测试标题**: 验证全选后删除所有商品  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有多个商品

**测试步骤**:
1. 点击"全选"复选框
2. 确认所有商品都被选中
3. 点击"删除选中商品"按钮
4. 确认删除操作
5. 检查购物车状态

**预期结果**:
- 所有商品都被选中
- 点击删除后，购物车中所有商品被清空
- 显示购物车为空的状态

---

## 4. 购物车编辑商品数量功能测试

### 4.1 按钮编辑数量测试

#### TC025 - 使用按钮编辑数量（正常范围）
**测试用例ID**: TC025  
**测试标题**: 验证使用+/-按钮编辑商品数量  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有商品，当前数量为5

**测试步骤**:
1. 点击"+"按钮2次
2. 点击"-"按钮1次
3. 检查最终数量

**预期结果**:
- 数量变为6（5+2-1=6）
- 小计和总价相应更新

---

#### TC026 - 数量为1时减号按钮失效
**测试用例ID**: TC026  
**测试标题**: 验证数量为1时-按钮不可用  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中商品数量为1

**测试步骤**:
1. 检查"-"按钮状态
2. 尝试点击"-"按钮

**预期结果**:
- "-"按钮显示为不可用状态
- 点击无效果，数量保持为1

---

#### TC027 - 达到上限时加号按钮失效
**测试用例ID**: TC027  
**测试标题**: 验证达到数量上限时+按钮不可用  
**优先级**: 高  

**测试数据**:
- 商品库存: 10件
- 当前数量: 10件

**前置条件**:
- 用户已登录
- 商品数量已达到库存上限

**测试步骤**:
1. 检查"+"按钮状态
2. 尝试点击"+"按钮

**预期结果**:
- "+"按钮显示为不可用状态
- 点击无效果，数量保持不变

---

### 4.2 直接编辑数量测试

#### TC028 - 直接修改数量（有效范围）
**测试用例ID**: TC028  
**测试标题**: 验证直接修改编辑框中的数量  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 点击数量编辑框
2. 清空原数量，输入"8"
3. 点击其他区域或按回车确认

**预期结果**:
- 数量成功更新为8
- 小计和总价相应更新

---

#### TC029 - 输入小于1的数量
**测试用例ID**: TC029  
**测试标题**: 验证输入小于1的数量时的处理  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 在数量编辑框中输入"0"
2. 确认输入

**预期结果**:
- 显示错误提示信息
- 数量自动恢复为1或保持原值

---

#### TC030 - 输入大于200的数量
**测试用例ID**: TC030  
**测试标题**: 验证输入大于200的数量时的处理  
**优先级**: 高  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 在数量编辑框中输入"300"
2. 确认输入

**预期结果**:
- 显示错误提示信息
- 数量自动调整为200或库存数量（取较小值）

---

#### TC031 - 输入超过库存的数量
**测试用例ID**: TC031  
**测试标题**: 验证输入超过库存数量时的处理  
**优先级**: 高  

**测试数据**:
- 商品库存: 15件

**前置条件**:
- 用户已登录
- 商品库存为15件

**测试步骤**:
1. 在数量编辑框中输入"20"
2. 确认输入

**预期结果**:
- 显示库存不足的提示信息
- 数量自动调整为库存数量（15件）

---

## 5. 边界条件和异常情况测试

### 5.1 网络异常测试

#### TC032 - 网络中断时的操作
**测试用例ID**: TC032  
**测试标题**: 验证网络中断时购物车操作的处理  
**优先级**: 中  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 断开网络连接
2. 尝试修改商品数量
3. 尝试删除商品
4. 恢复网络连接

**预期结果**:
- 显示网络错误提示
- 操作失败，数据保持原状
- 网络恢复后可正常操作

---

### 5.2 并发操作测试

#### TC033 - 同时修改同一商品数量
**测试用例ID**: TC033  
**测试标题**: 验证多个操作同时修改同一商品的处理  
**优先级**: 低  

**前置条件**:
- 用户已登录
- 购物车中有商品

**测试步骤**:
1. 快速连续点击"+"按钮多次
2. 检查最终数量是否正确

**预期结果**:
- 数量正确累加，不出现数据错误
- 界面显示与实际数据一致

---

## 测试总结

本测试用例文档涵盖了购物车功能的以下方面：

1. **显示功能**: 登录状态判断、商品信息展示、价格计算
2. **添加功能**: 商品添加、数量设置、限制验证
3. **删除功能**: 单个删除、批量删除、全选删除
4. **编辑功能**: 数量修改、边界检查、库存验证
5. **异常处理**: 网络异常、并发操作、边界条件

总计33个测试用例，覆盖了购物车功能的主要业务逻辑和边界情况，确保系统的稳定性和用户体验。
