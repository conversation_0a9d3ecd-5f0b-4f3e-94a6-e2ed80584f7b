测试用例ID,功能模块,测试标题,优先级,前置条件,测试步骤,预期结果,测试数据,备注
TC001,购物车显示,验证未登录用户访问购物车时的提示信息,高,"用户未登录系统;用户访问购物车页面","1. 打开购物车页面
2. 检查页面显示内容","显示登录提示信息：""购物车内暂时没有商品，登录后将显示您之前加入的商品"";提供登录入口或按钮",,未登录状态测试
TC002,购物车显示,验证已登录用户购物车为空时的显示,高,"用户已成功登录;购物车中没有任何商品","1. 登录系统
2. 访问购物车页面
3. 检查页面显示内容","显示购物车为空的提示;提供""去购物""的引导按钮或链接",,已登录状态测试
TC003,购物车显示,验证购物车商品信息正确显示,高,"用户已成功登录;购物车中已有商品","1. 登录系统
2. 向购物车添加商品
3. 访问购物车页面
4. 检查商品信息显示","显示商品图片、名称、价格等基本信息;显示商品数量和小计金额;显示总计信息",,已登录状态测试
TC004,购物车显示,验证购物车中商品价格与加入时一致,高,"用户已登录;商品详情页显示价格为100元","1. 在商品详情页查看商品价格
2. 点击""加入购物车""
3. 进入购物车页面
4. 检查商品价格显示",购物车中显示的商品价格与加入时的价格完全一致,商品价格: 100元,价格一致性验证
TC005,购物车显示,验证商品小计计算正确性,高,"用户已登录;购物车中有商品","1. 在购物车中选择数量为3的商品（单价50元）
2. 检查小计显示",小计显示为150元（50 × 3 = 150）,"商品单价: 50元;商品数量: 3件",小计金额计算验证
TC006,购物车显示,验证已选择商品件数统计正确,中,"用户已登录;购物车中有多种商品","1. 选中商品A和商品B
2. 不选中商品C
3. 检查""已选择n件商品""显示",显示"已选择5件商品"（2+3=5）,"商品A: 2件（已选中）;商品B: 3件（已选中）;商品C: 1件（未选中）",已选择商品件数统计
TC007,购物车显示,验证购物车总价计算正确,高,"用户已登录;购物车中有多种商品","1. 选中商品A和商品B
2. 不选中商品C
3. 检查总价显示",总价显示为100元（30×2 + 40×1 = 100）,"商品A: 单价30元，数量2件，已选中;商品B: 单价40元，数量1件，已选中;商品C: 单价20元，数量3件，未选中",总价计算验证
TC008,购物车显示,验证点击商品图片跳转到商品详情页,中,"用户已登录;购物车中有商品","1. 在购物车页面点击商品图片
2. 检查页面跳转",成功跳转到对应商品的详情页面,,商品图片点击跳转
TC009,购物车显示,验证点击商品名称跳转到商品详情页,中,"用户已登录;购物车中有商品","1. 在购物车页面点击商品名称
2. 检查页面跳转",成功跳转到对应商品的详情页面,,商品名称点击跳转
TC010,购物车显示,验证点击去结算按钮跳转到订单页面,高,"用户已登录;购物车中有已选中的商品","1. 选中购物车中的商品
2. 点击""去结算""按钮
3. 检查页面跳转",成功跳转到填写核对订单页面,,去结算按钮功能
TC011,购物车显示,验证抢购秒杀商品优惠金额显示,中,"用户已登录;购物车中有抢购秒杀商品","1. 将秒杀商品加入购物车
2. 选中该商品
3. 检查""已节省""金额显示",显示"已节省40元"（(100-80)×2 = 40）,"秒杀商品原价: 100元;秒杀价格: 80元;商品数量: 2件",已节省金额显示
TC012,购物车添加,验证从商品详情页添加商品到购物车,高,"用户已登录;在商品详情页面","1. 在商品详情页面点击""加入购物车""按钮
2. 进入购物车页面检查","商品成功添加到购物车;购物车中显示该商品信息",,从商品页面加入购物车
TC013,购物车添加,验证直接输入购买数量功能,高,"用户已登录;在商品详情页面或购物车页面","1. 在数量输入框中直接输入数字""5""
2. 确认输入","数量成功设置为5;相关计算（小计等）自动更新",,直接输入数量
TC014,购物车添加,验证点击+号按钮增加商品数量,高,"用户已登录;商品当前数量为1","1. 点击数量旁的""+""按钮
2. 检查数量变化","商品数量增加到2;小计金额相应更新",,使用加号按钮增加数量
TC015,购物车添加,验证点击-号按钮减少商品数量,高,"用户已登录;商品当前数量为3","1. 点击数量旁的""-""按钮
2. 检查数量变化","商品数量减少到2;小计金额相应更新",,使用减号按钮减少数量
TC016,购物车添加,验证商品数量不能小于1,高,"用户已登录;商品当前数量为1","1. 尝试点击""-""按钮
2. 检查按钮状态和数量","""-""按钮处于失效状态，无法点击;商品数量保持为1",,最小数量限制测试
TC017,购物车添加,验证商品数量不能超过200件,高,"用户已登录;商品当前数量为200;商品库存充足","1. 尝试点击""+""按钮
2. 检查按钮状态和数量","""+""按钮处于失效状态，无法点击;商品数量保持为200",,最大数量限制测试（200件）
TC018,购物车添加,验证商品数量不能超过库存,高,"用户已登录;商品库存为5件;购物车中该商品数量为5件","1. 尝试点击""+""按钮
2. 检查按钮状态和数量","""+""按钮处于失效状态，无法点击;商品数量保持为5",商品库存: 5件;当前购物车数量: 5件,库存数量限制测试
TC019,购物车添加,验证直接输入超出范围数量的处理,高,"用户已登录;在购物车页面","1. 在数量输入框中输入""0""
2. 确认输入
3. 检查系统反应","显示错误提示信息;数量恢复到有效值（如1）",,直接输入超出范围数量测试
TC020,购物车添加,验证直接输入超过200的数量处理,高,"用户已登录;在购物车页面","1. 在数量输入框中输入""250""
2. 确认输入
3. 检查系统反应","显示错误提示信息;数量恢复到有效值（如200或库存数量）",,直接输入超过200的数量测试
TC021,购物车添加,验证购物车商品种类不能超过20种,中,"用户已登录;购物车中已有20种不同商品","1. 尝试添加第21种商品到购物车
2. 检查系统反应","显示提示信息，告知商品种类已达上限;第21种商品添加失败",,商品种类数量限制测试
TC022,购物车删除,验证删除购物车中单个商品,高,"用户已登录;购物车中有多个商品","1. 点击某个商品操作栏中的""删除""按钮
2. 确认删除操作
3. 检查购物车内容","对应商品从购物车中被删除;其他商品保持不变;总价等信息自动更新",,删除单个商品
TC023,购物车删除,验证批量删除选中商品功能,高,"用户已登录;购物车中有多个商品","1. 勾选多个商品（不是全部）
2. 点击""删除选中商品""按钮
3. 确认删除操作
4. 检查购物车内容","选中的商品全部被删除;未选中的商品保持不变;相关统计信息自动更新",,删除选中的多个商品
TC024,购物车删除,验证全选后删除所有商品,高,"用户已登录;购物车中有多个商品","1. 点击""全选""复选框
2. 确认所有商品都被选中
3. 点击""删除选中商品""按钮
4. 确认删除操作
5. 检查购物车状态","所有商品都被选中;点击删除后，购物车中所有商品被清空;显示购物车为空的状态",,全选删除功能测试
TC025,购物车编辑,验证使用+/-按钮编辑商品数量,高,"用户已登录;购物车中有商品，当前数量为5","1. 点击""+""按钮2次
2. 点击""-""按钮1次
3. 检查最终数量","数量变为6（5+2-1=6）;小计和总价相应更新",,使用按钮编辑数量（正常范围）
TC026,购物车编辑,验证数量为1时-按钮不可用,高,"用户已登录;购物车中商品数量为1","1. 检查""-""按钮状态
2. 尝试点击""-""按钮","""-""按钮显示为不可用状态;点击无效果，数量保持为1",,数量为1时减号按钮失效
TC027,购物车编辑,验证达到数量上限时+按钮不可用,高,"用户已登录;商品数量已达到库存上限","1. 检查""+""按钮状态
2. 尝试点击""+""按钮","""+""按钮显示为不可用状态;点击无效果，数量保持不变","商品库存: 10件;当前数量: 10件",达到上限时加号按钮失效
TC028,购物车编辑,验证直接修改编辑框中的数量,高,"用户已登录;购物车中有商品","1. 点击数量编辑框
2. 清空原数量，输入""8""
3. 点击其他区域或按回车确认","数量成功更新为8;小计和总价相应更新",,直接修改数量（有效范围）
TC029,购物车编辑,验证输入小于1的数量时的处理,高,"用户已登录;购物车中有商品","1. 在数量编辑框中输入""0""
2. 确认输入","显示错误提示信息;数量自动恢复为1或保持原值",,输入小于1的数量
TC030,购物车编辑,验证输入大于200的数量时的处理,高,"用户已登录;购物车中有商品","1. 在数量编辑框中输入""300""
2. 确认输入","显示错误提示信息;数量自动调整为200或库存数量（取较小值）",,输入大于200的数量
TC031,购物车编辑,验证输入超过库存数量时的处理,高,"用户已登录;商品库存为15件","1. 在数量编辑框中输入""20""
2. 确认输入","显示库存不足的提示信息;数量自动调整为库存数量（15件）",商品库存: 15件,输入超过库存的数量
TC032,异常处理,验证网络中断时购物车操作的处理,中,"用户已登录;购物车中有商品","1. 断开网络连接
2. 尝试修改商品数量
3. 尝试删除商品
4. 恢复网络连接","显示网络错误提示;操作失败，数据保持原状;网络恢复后可正常操作",,网络中断时的操作
TC033,异常处理,验证多个操作同时修改同一商品的处理,低,"用户已登录;购物车中有商品","1. 快速连续点击""+""按钮多次
2. 检查最终数量是否正确","数量正确累加，不出现数据错误;界面显示与实际数据一致",,同时修改同一商品数量
